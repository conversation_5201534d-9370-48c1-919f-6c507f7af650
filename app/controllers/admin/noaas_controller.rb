# frozen_string_literal: true

module Admin
  class NoaasController < BaseController
    NOAA_DOC_TYPE = DocTemplate::TYPES[:CRB_AA]
    PRESIGNED_URL_EXPIRATION = 10.minutes.to_i

    before_action :initialize_search_form_model

    def index; end

    def search
      return if params[:clear]

      return handle_form_error(@search_form_model) if @search_form_model.invalid?

      @noaa_details = noaa_details_from_loan_id.presence || noaa_details_from_document_identifiers
    end

    private

    def initialize_search_form_model
      @search_form_model = if params[:clear]
                             Admin::NoaaSearchFormModel.new
                           else
                             Admin::NoaaSearchFormModel.new(**search_params)
                           end
    rescue ActionController::ParameterMissing
      @search_form_model = Admin::NoaaSearchFormModel.new
    end

    def search_params
      params.require(:admin_noaa_search_form_model)
            .permit(:loan_identifier, :first_name, :last_name, :document_created_on)
    end

    def handle_form_error(form_model)
      @noaa_details = []
      @error_message = form_model.errors.full_messages.to_sentence
    end

    def noaa_details_from_loan_id
      return [] unless loan

      doc = find_doc_by_loan_id
      status = loan.loan_status_histories.first

      [{
        noaa_document_name: doc.name,
        noaa_created_at: doc.created_at,
        noaa_download_url: presigned_url_for(doc),
        current_loan_status: status.new_status,
        loan_status_reached_at: status.updated_at
      }]
    end

    def find_doc_by_loan_id
      loan.docs.joins(:template).find_by(template: { type: NOAA_DOC_TYPE })
    end

    def loan
      return @loan if defined?(@loan)

      id = search_params[:loan_identifier].presence
      return unless id

      @loan = Loan.includes(:loan_status_histories, :docs)
                  .where(unified_id: id)
                  .or(Loan.where(id: id))
                  .or(Loan.where(request_id: id))
                  .first
    end

    def presigned_url_for(doc)
      Aws::S3::Presigner.new.presigned_url(
        :get_object,
        bucket: config.bucket_name,
        key: doc.uri,
        expires_in: PRESIGNED_URL_EXPIRATION
      )
    end

    def noaa_details_from_document_identifiers
      docs = find_docs_by_document_identifiers

      docs.map do |doc|
        {
          noaa_document_name: doc.name,
          noaa_created_at: doc.created_at,
          noaa_download_url: presigned_url_for(doc)
        }
      end
    end

    def find_docs_by_document_identifiers
      first_name = search_params[:first_name]
      last_name = search_params[:last_name]
      document_created_on = search_params[:document_created_on]

      return [] unless first_name.present? || last_name.present?

      date = Date.parse(document_created_on) if document_created_on.present?

      query = Doc.includes(loan: :loan_status_histories)
                 .joins(:template)
                 .where(template: { type: NOAA_DOC_TYPE })
                 .where('docs.name ILIKE ?', "%#{first_name}_#{last_name}%")

      query = query.where('DATE(docs.created_at) = ?', date) if date

      query.order(created_at: :desc).limit(10)
    end

    def config
      @config ||= Rails.application.config_for(:contract_documents)
    end
  end
end
